from django.core.exceptions import ValidationError
from django.db import models

from re import match

from natsort import natsorted
from rahavard import (
    create_short_uuid,
    sort_dict,
)

MODELS_MANAGER = models.Manager()


def validate_is_ip(value):
    ## imported inside function to avoid circular import
    from .utils_ip import is_ip
    if not is_ip(value):
        raise ValidationError(f"'{value}' is not a valid IP")


allowed_chars_msg = 'Allowed characters: letters (AaBbCc...), numbers (012...) and hyphen (-)'
def validate_has_allowed_chars(value):
    '''
    allowed characters:
      - `a-z`
      - `A-Z`
      - `0-9`
      - `-` (hyphen)

    __DATABASE_YMD_PATTERN__
    Q: why is _ not allowed?
    A: names should not have _ because
       - when creating name of database
         in base/utils.py@create_name_of_database,
         we - -> _ in object_name and ymd
       - when extracting path from name of database
         in base/utils.py@get_directory_path_from_name_of_database,
         we _ -> -
       so if the original name has _
       we will end up with incorrect values
    '''

    if match(r'^[a-zA-Z0-9-]+$', value) is None:
        raise ValidationError(f'"{value}" did not match allowed characters')


class CommonMethods:
    @property
    def model_name(self):
        return self.__class__.__name__

    @classmethod
    def get_list_of_names(cls):
        objects = cls.active_objects.all()

        if not objects:
            return []

        return natsorted(objects.values_list('name', flat=True))
        ## ['Sensor-1', 'Sensor-2', ...]

    @classmethod
    def get_list_of_addresses(cls):
        objects = cls.active_objects.all()

        if not objects:
            return []

        return natsorted(objects.values_list('address', flat=True))
        ## ['***********', '***********', ...]

    @classmethod
    def get_list_of_names_and_addresses(cls):
        objects = cls.active_objects.all()

        if not objects:
            return []

        return natsorted([
            item for tup
            in dict(objects.values_list('name', 'address')).items()
            for item in tup
        ])
        ## ['***********', 'Sensor-1', '***********', 'Sensor-2', ...]

    @classmethod
    def get_dict_of_addresses_and_names(cls):
        objects = cls.active_objects.all()

        if not objects:
            return {}

        return sort_dict(
            dict(objects.values_list('address', 'name')),
            based_on='key',
            reverse=False,
        )
        ## {
        ##     '***********': 'Sensor-1',
        ##     '***********': 'Sensor-2',
        ##     ...,
        ## }

class GetActiveObjects(models.Manager):
    def get_queryset(self):

        ## https://stackoverflow.com/questions/67979442/how-do-i-find-the-class-that-relatedmanager-is-managing-when-the-queryset-is-emp
        _caller = self.model.__name__  ## 'User'/'Router'/...  (-> is str)

        if _caller == 'User':
            return super(GetActiveObjects, self).get_queryset().filter(is_active=True)

        return super(GetActiveObjects, self).get_queryset().filter(active=True)

class Module(models.Model, CommonMethods):
    class Meta:
        ordering = ['id']  ## oldest on top

    name        = models.CharField(max_length=200, unique=True, validators=[validate_has_allowed_chars], help_text=f'e.g. Snort [{allowed_chars_msg}]')
    # slug        = models.SlugField(max_length=200, unique=True, validators=[validate_has_allowed_chars], help_text=f'e.g. snort [{allowed_chars_msg}]')
    description = models.TextField(null=True, blank=True)
    active      = models.BooleanField(default=True)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created    = models.DateTimeField(auto_now_add=True)
    updated    = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    def __str__(self):
        return self.name

class Sensor(models.Model, CommonMethods):
    class Meta:
        ordering = ['id']  ## oldest on top

    name        = models.CharField(max_length=50, unique=True, validators=[validate_has_allowed_chars], help_text=f'e.g. Sensor-One [{allowed_chars_msg}]')
    address     = models.CharField(max_length=50, unique=True, blank=True, null=True, validators=[validate_is_ip], help_text='e.g. ***********')
    description = models.TextField(null=True, blank=True)
    active      = models.BooleanField(default=True)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created    = models.DateTimeField(auto_now_add=True)
    updated    = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    def __str__(self):
        return self.name

class Switch(models.Model, CommonMethods):
    class Meta:
        verbose_name_plural = 'Switches'
        ordering = ['id']  ## oldest on top

    name        = models.CharField(max_length=50, unique=True, validators=[validate_has_allowed_chars], help_text=f'e.g. Switch-One [{allowed_chars_msg}]')
    address     = models.CharField(max_length=50, unique=True, blank=True, null=True, validators=[validate_is_ip], help_text='e.g. ***********')
    description = models.TextField(null=True, blank=True)
    active      = models.BooleanField(default=True)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created    = models.DateTimeField(auto_now_add=True)
    updated    = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    def __str__(self):
        return self.name

class Router(models.Model, CommonMethods):
    class Meta:
        ordering = ['id']  ## oldest on top

    name        = models.CharField(max_length=50, unique=True, validators=[validate_has_allowed_chars], help_text=f'e.g. Router-One [{allowed_chars_msg}]')
    address     = models.CharField(max_length=50, unique=True, blank=True, null=True, validators=[validate_is_ip], help_text='e.g. ***********')
    description = models.TextField(null=True, blank=True)
    active      = models.BooleanField(default=True)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created    = models.DateTimeField(auto_now_add=True)
    updated    = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    def __str__(self):
        return self.name

class VMware(models.Model, CommonMethods):
    class Meta:
        verbose_name_plural = 'VMwares'
        ordering = ['id']  ## oldest on top

    name        = models.CharField(max_length=50, unique=True, validators=[validate_has_allowed_chars], help_text=f'e.g. VMware-One [{allowed_chars_msg}]')
    address     = models.CharField(max_length=50, unique=True, blank=True, null=True, validators=[validate_is_ip], help_text='e.g. ***********')
    description = models.TextField(null=True, blank=True)
    active      = models.BooleanField(default=True)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created    = models.DateTimeField(auto_now_add=True)
    updated    = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    def __str__(self):
        return self.name

class WindowsServer(models.Model, CommonMethods):
    class Meta:
        verbose_name_plural = 'Windows Servers'
        ordering = ['id']  ## oldest on top

    name        = models.CharField(max_length=50, unique=True, validators=[validate_has_allowed_chars], help_text=f'e.g. WindowsServer-One [{allowed_chars_msg}]')
    address     = models.CharField(max_length=50, unique=True, blank=True, null=True, validators=[validate_is_ip], help_text='e.g. ***********')
    description = models.TextField(null=True, blank=True)
    active      = models.BooleanField(default=True)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created    = models.DateTimeField(auto_now_add=True)
    updated    = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    def __str__(self):
        return self.name

class RouterBoard(models.Model, CommonMethods):
    class Meta:
        verbose_name_plural = 'RouterBoards'
        ordering = ['id']  ## oldest on top

    name        = models.CharField(max_length=50, unique=True, validators=[validate_has_allowed_chars], help_text=f'e.g. RouterBoard-One [{allowed_chars_msg}]')
    address     = models.CharField(max_length=50, unique=True, blank=True, null=True, validators=[validate_is_ip], help_text='e.g. ***********')
    description = models.TextField(null=True, blank=True)
    active      = models.BooleanField(default=True)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created    = models.DateTimeField(auto_now_add=True)
    updated    = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    def __str__(self):
        return self.name

class Gateway(models.Model, CommonMethods):
    class Meta:
        ordering = ['id']  ## oldest on top

    name        = models.CharField(max_length=50, unique=True, help_text='e.g. GW One, GW-One, etc.')
    address     = models.CharField(max_length=50, unique=True, blank=True, null=True, validators=[validate_is_ip], help_text='e.g. ***********')
    description = models.TextField(null=True, blank=True)
    active      = models.BooleanField(default=True)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created    = models.DateTimeField(auto_now_add=True)
    updated    = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    def __str__(self):
        return self.name

class Interface(models.Model, CommonMethods):
    class Meta:
        ordering = ['id']  ## oldest on top

    ## do NOT add unique=True
    real_interface = models.CharField(max_length=50, help_text='e.g. em1', verbose_name='Real Interface')

    ## do NOT add unique=True
    interface = models.CharField(max_length=50, help_text='e.g. LAN')

    ## an Interface instance can have multiple sensors
    sensors = models.ManyToManyField(Sensor, blank=True)  ## null=True removed because of this warning: (fields.W340) null has no effect on ManyToManyField

    description = models.TextField(null=True, blank=True)

    active = models.BooleanField(default=True)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    def __str__(self):
        return f'{self.real_interface} - {self.interface}'

    ## used in admin.py
    ##      in overview.html
    @property
    def sensors__(self):
        return ', '.join(sorted(self.sensors.values_list('name', flat=True)))

class FirewallRule(models.Model, CommonMethods):
    class Meta:
        verbose_name_plural = 'Firewall Rules'
        ordering = ['id']  ## oldest on top

    tracking_id = models.IntegerField(unique=True, verbose_name='Tracking ID')

    ## do NOT add unique=True
    name = models.CharField(max_length=20, help_text='e.g. APAC1200')

    ## do NOT add unique=True
    interface = models.ForeignKey(Interface, null=True, related_name='firewallrule_interface_rel', on_delete=models.SET_NULL)  ## have to use null=True because on_delete=models.SET_NULL

    description = models.TextField(null=True, blank=True)

    active = models.BooleanField(default=True)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    ## used in admin.py
    ##      in overview.html
    @property
    def interface__(self):
        return self.interface.interface

    def __str__(self):
        return f'{self.tracking_id} - {self.name}'

class StaticIP(models.Model, CommonMethods):
    class Meta:
        verbose_name_plural = 'Static IPs'
        ordering = ['id']  ## oldest on top

    ## do NOT add blank=True, null=True
    computer_name = models.CharField(max_length=50, unique=True, help_text='e.g. some-custom-name', verbose_name='Computer Name')

    ## do NOT add blank=True, null=True
    ## do NOT add unique=True because different computer_name (i.e. dc01 and dc02) can have the same real_name (i.e. ADDS)
    real_name = models.CharField(max_length=50, help_text='e.g. Jane Austen', verbose_name='Real Name')

    ## keep blank=True, null=True
    address = models.CharField(max_length=50, unique=True, blank=True, null=True, validators=[validate_is_ip], help_text='e.g. ***********')

    ## keep blank=True, null=True
    virtual_address = models.CharField(max_length=50, unique=True, blank=True, null=True, validators=[validate_is_ip], help_text='e.g. ***********', verbose_name='Virtual Address')

    description = models.TextField(null=True, blank=True)

    active = models.BooleanField(default=True)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    def __str__(self):
        return self.computer_name

class PublicIP(models.Model, CommonMethods):
    class Meta:
        verbose_name_plural = 'Public IPs'
        ordering = ['id']  ## oldest on top

    address = models.CharField(max_length=50, unique=True, validators=[validate_is_ip], help_text='e.g. 123.456.789.0')

    description = models.TextField(null=True, blank=True)

    active = models.BooleanField(default=True)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    def __str__(self):
        return self.address

class FireHOL(models.Model, CommonMethods):
    class Meta:
        verbose_name_plural = 'FireHOLs'
        ordering = ['id']  ## oldest on top

    name        = models.CharField(max_length=50, unique=True, help_text='e.g. GitHub Source 1, etc.')
    url         = models.CharField(max_length=200, unique=True, help_text='e.g. https://example.com/hosts.txt', verbose_name='URL')
    description = models.TextField(null=True, blank=True)
    active      = models.BooleanField(default=True)

    short_uuid = models.CharField(default=create_short_uuid, unique=True, editable=False, max_length=10, verbose_name='Short UUID')
    created    = models.DateTimeField(auto_now_add=True)
    updated    = models.DateTimeField(auto_now=True)

    ## managers
    objects        = MODELS_MANAGER
    active_objects = GetActiveObjects()

    def __str__(self):
        return self.name
