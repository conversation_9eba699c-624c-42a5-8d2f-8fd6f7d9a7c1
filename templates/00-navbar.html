{% load static %}
{% load humanize %}
{% load tags-filters %}

<!-- app-header -->
 <header class="app-header">

    <!-- Start::main-header-container -->
    <div class="main-header-container container-fluid">

        <!-- Start::header-content-left -->
        <div class="header-content-left">

            <!-- Start::header-element -->
            <div class="header-element     my-auto">
                <div class="horizontal-logo">
                    <a href="{% url "general-homepage-url" %}" class="header-logo">
                        <img src="{% static "files/project-logo/logo.png" %}" alt="logo" class="desktop-logo">
                        <img src="{% static "files/project-logo/logo.png" %}" alt="logo" class="toggle-logo">
                        <img src="{% static "files/project-logo/logo.png" %}" alt="logo" class="desktop-dark">
                        <img src="{% static "files/project-logo/logo.png" %}" alt="logo" class="toggle-dark">
                    </a>
                </div>
            </div>
            <!-- End::header-element -->

            <!-- Start::header-element -->
            <div class="header-element mx-lg-0 mx-2">
                <a aria-label="Hide Sidebar" class="sidemenu-toggle header-link animated-arrow hor-toggle horizontal-navtoggle" data-bs-toggle="sidebar" href="javascript:void(0);"><span></span></a>
            </div>
            <!-- End::header-element -->

            <!-- Start::header-element --> 
            {% comment %}
                NOTE INVISIBLE_ELEMENT
                     this element is not needed but we kept it
                     because udon/dist/assets/js/custom.js would throw error
                     for not finding it and thus breaking
                     so we just "d-lg-block d-none" -> "d-none"
                     to make it invisible
            {% endcomment %}
            <div class="header-element ms-3 my-auto     d-none">  {# "d-lg-block d-none" -> "d-none" #}
                <!-- Start::dashboards list -->
                <div class="dropdown my-auto">
                    <a href="javascript:void(0);" class="btn bg-body header-dashboards-button text-start d-flex align-items-center justify-content-between" data-bs-toggle="dropdown" aria-expanded="false">
                    </a> 
                    <ul class="dropdown-menu dashboard-dropdown" role="menu">
                        <li><a class="dropdown-item dashboards-dropdown-item" href="index.html">Sales Dashboard</a></li>
                        <li><a class="dropdown-item dashboards-dropdown-item" href="index-1.html">Analytics Dashboard</a></li>
                        <li><a class="dropdown-item dashboards-dropdown-item" href="index-2.html">Ecommerce Dashboard</a></li>
                        <li><a class="dropdown-item dashboards-dropdown-item" href="index-3.html">CRM Dashboard</a></li>
                        <li><a class="dropdown-item dashboards-dropdown-item" href="index-4.html">HRM Dashboard</a></li>
                        <li><a class="dropdown-item dashboards-dropdown-item" href="index-5.html">NFT Dashboard</a></li>
                        <li><a class="dropdown-item dashboards-dropdown-item" href="index-6.html">Crypto Dashboard</a></li>
                        <li><a class="dropdown-item dashboards-dropdown-item" href="index-7.html">Jobs Dashboard</a></li>
                        <li><a class="dropdown-item dashboards-dropdown-item" href="index-8.html">Projects Dashboard</a></li>
                        <li><a class="dropdown-item dashboards-dropdown-item" href="index-9.html">Courses Dashboard</a></li>
                        <li><a class="dropdown-item dashboards-dropdown-item" href="index-10.html">Stocks Dashboard</a></li>
                        <li><a class="dropdown-item dashboards-dropdown-item" href="index-11.html">Personal Dashboard</a></li>
                        <li><a class="dropdown-item dashboards-dropdown-item" href="index-12.html">Customer Dashboard</a></li>
                    </ul>
                </div>
                <!-- End::dashboards list -->
            </div>
            <!-- End::header-element -->

{# main title #}
            <div class="header-element     ms-md-3 ms-1">{% include '00-header-title.html' with mode="header-row" ttl=main_title %}</div>

        </div>
        <!-- End::header-content-left -->

{# ------------------------------------------------------------------- #}

        <!-- Start::header-content-right -->
        <ul class="header-content-right     align-items-center">

            <!-- Start::header-element -->
            {% comment %}
            <li class="header-element d-md-none d-block">
                <a href="javascript:void(0);" class="header-link" data-bs-toggle="modal" data-bs-target="#header-responsive-search">
                    <!-- Start::header-link-icon -->
                    <i class="bi bi-search header-link-icon"></i>
                    <!-- End::header-link-icon -->
                </a>  
            </li>
            {% endcomment %}
            <!-- End::header-element -->

{# dark/light switch #}
            <!-- Start::header-element -->
            <li class="header-element header-theme-mode"
                data-bs-toggle="tooltip"
                data-bs-placement="left"
                data-bs-trigger="hover"
                data-bs-custom-class="tooltip-dark"
                title="Dark/Light Mode"
            >
                <!-- Start::header-link|layout-setting -->
                <a href="javascript:void(0);" class="header-link layout-setting">
                    <span class="light-layout">
                        <!-- Start::header-link-icon -->
                        <i class="bi bi-moon header-link-icon"></i>
                        <!-- End::header-link-icon -->
                    </span>
                    <span class="dark-layout">
                        <!-- Start::header-link-icon -->
                        <i class="bi bi-brightness-high header-link-icon"></i>
                        <!-- End::header-link-icon -->
                    </span>
                </a>
                <!-- End::header-link|layout-setting -->
            </li>
            <!-- End::header-element -->

{# full screen #}
            <!-- Start::header-element -->
            <li class="header-element header-fullscreen"
                data-bs-toggle="tooltip"
                data-bs-placement="left"
                data-bs-trigger="hover"
                data-bs-custom-class="tooltip-dark"
                title="Full Screen"
            >
                <!-- Start::header-link -->
                <a onclick="openFullscreen();" href="javascript:void(0);" class="header-link">
                    <i class="bi bi-fullscreen full-screen-open header-link-icon"></i>
                    <i class="bi bi-fullscreen-exit full-screen-close header-link-icon d-none"></i>
                </a>
                <!-- End::header-link -->
            </li>
            <!-- End::header-element -->

{# daemon errors count #}
            <!-- Start::header-element -->
            <li class="header-element cart-dropdown"
                data-bs-toggle="tooltip"
                data-bs-placement="left"
                data-bs-trigger="hover"
                data-bs-custom-class="tooltip-dark"
                title="Daemon Errors"
            >
                <!-- Start::header-link|dropdown-toggle -->
                <a href="javascript:void(0);" class="header-link dropdown-toggle" data-bs-auto-close="outside" data-bs-toggle="dropdown">
                    <i class="bi bi-exclamation-circle header-link-icon"></i>
                    {% if daemon_errors_count > 0 %}
                        <span class="badge bg-danger rounded-pill header-icon-badge" id="cart-icon-badge">{{daemon_errors_count|intcomma}}</span>
                    {% endif %}
                </a>
                <!-- End::header-link|dropdown-toggle -->
                <!-- Start::main-header-dropdown -->
                <div class="main-header-dropdown dropdown-menu dropdown-menu-end" data-popper-placement="none">
                    <div class="p-3">
                        <div class="d-flex align-items-center justify-content-between">
                            <p class="mb-0 fs-16">Daemon Errors
                            {% if daemon_errors_count > 0 %}
                                <span class="badge bg-danger-transparent ms-1 fs-12 rounded-circle" id="cart-data">{{daemon_errors_count|intcomma}}</span></p>
                            {% else %}
                                <span class="badge bg-secondary-transparent ms-1 fs-12 rounded-circle" id="cart-data">0</span></p>
                            {% endif %}
                            <span><span class="text-muted me-1">Date:</span><span class="text-primary fw-medium">{{yesterday_ymd}}</span></span>
                        </div>
                    </div>
                    {# <div class="dropdown-divider"></div> #}

                    {% comment %}
                        do NOT remove this ul otherwise
                        udon/dist/assets/js/custom.js would throw error
                    {% endcomment %}
                    <ul class="list-unstyled mb-0" id="header-cart-items-scroll">
                        {# removed what's inside by me #}
                    </ul>

                    <div class="p-3 empty-header-item border-top">
                        <div class="text-center">
                            {% if daemon_errors_count > 0 %}
                                <a href="{% url "daemon-detailed-report-url" %}?date={{yesterday_ymd}}&Level=(daemon/err)" class="link-danger text-decoration-underline" onclick="resetDaemonErrorsCount()">See Detailed Report for Errors</a>
                            {% else %}
                                <a href="{% url "daemon-detailed-report-url" %}?date={{yesterday_ymd}}" class="link-success text-decoration-underline">See Detailed Report</a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <!-- End::main-header-dropdown -->
            </li>
            <!-- End::header-element -->

{# disk usage statistics #}
            <!-- Start::header-element -->
            <li class="header-element notifications-dropdown d-xl-block d-none"
                data-bs-toggle="tooltip"
                data-bs-placement="left"
                data-bs-trigger="hover"
                data-bs-custom-class="tooltip-dark"
                title="Disk Usage Statistics"
            >
                <!-- Start::header-link|dropdown-toggle -->
                <a href="javascript:void(0);" class="header-link dropdown-toggle" data-bs-toggle="dropdown" data-bs-auto-close="outside" id="messageDropdown" aria-expanded="false">
                    <i class="bi bi-device-ssd header-link-icon"></i>
                    {# <span class="header-icon-pulse bg-secondary rounded pulse pulse-secondary"></span> #}
                </a>
                <!-- End::header-link|dropdown-toggle -->
                <!-- Start::main-header-dropdown -->
                <div class="main-header-dropdown dropdown-menu dropdown-menu-end" data-popper-placement="none">
                    <div class="p-3">
                        <div class="d-flex align-items-center justify-content-between">
                            <p class="mb-0 fs-16">Disk Usage Statistics</p>
                            <span class="badge bg-secondary-transparent" id="notifiation-data"></span>
                        </div>
                    </div>
                    <div class="dropdown-divider"></div>

                    {% comment %}
                        do NOT remove this ul otherwise
                        udon/dist/assets/js/custom.js would throw error
                    {% endcomment %}
                    <ul class="list-unstyled mb-0" id="header-notification-scroll"     style="overflow-y:scroll;">
                        {# added 'text-dark bg-transparent' to avoid color change when hovered #}
                        <li class="dropdown-item     text-dark bg-transparent"
                            hx-get="{% url "base-get-disk-usage-statistics-url" %}"
                            hx-trigger="intersect once delay:1s"
                            hx-swap="outerHTML"
                        >
                            <p class="card-text placeholder-glow">
                                <span class="placeholder placeholder-xs col-7"></span>
                                <span class="placeholder placeholder-xs col-4"></span>
                                <span class="placeholder placeholder-xs col-4"></span>
                                <span class="placeholder placeholder-xs col-6"></span>
                            </p>
                        </li>
                    </ul>

                </div>
                <!-- End::main-header-dropdown -->
            </li>
            <!-- End::header-element -->

{# parsed dates #}
            <!-- Start::header-element -->
            <li class="header-element notifications-dropdown d-xl-block d-none"
                data-bs-toggle="tooltip"
                data-bs-placement="left"
                data-bs-trigger="hover"
                data-bs-custom-class="tooltip-dark"
                title="Parsed Dates"
            >
                <!-- Start::header-link|dropdown-toggle -->
                <a href="javascript:void(0);" class="header-link dropdown-toggle" data-bs-toggle="dropdown" data-bs-auto-close="outside" id="messageDropdown" aria-expanded="false">
                    <i class="bi bi-collection header-link-icon"></i>
                    {# <span class="header-icon-pulse bg-secondary rounded pulse pulse-secondary"></span> #}
                </a>
                <!-- End::header-link|dropdown-toggle -->
                <!-- Start::main-header-dropdown -->
                <div class="main-header-dropdown dropdown-menu dropdown-menu-end" data-popper-placement="none">
                    <div class="p-3">
                        <div class="d-flex align-items-center justify-content-between">
                            <p class="mb-0 fs-16">Parsed Dates</p>
                            <span class="badge bg-secondary-transparent" id="notifiation-data"></span>
                        </div>
                    </div>
                    <div class="dropdown-divider"></div>

                    {% comment %}
                        do NOT remove this ul otherwise
                        udon/dist/assets/js/custom.js would throw error
                    {% endcomment %}
                    <ul class="list-unstyled mb-0" id="header-notification-scroll"     style="overflow-y:scroll;">
                        {# added 'text-dark bg-transparent' to avoid color change when hovered #}
                        <li class="dropdown-item     text-dark bg-transparent"
                            hx-get="{% url "base-get-parsed-dates-statistics-url" %}"
                            hx-trigger="intersect once delay:1s"
                            hx-swap="outerHTML"
                        >
                            <p class="card-text placeholder-glow">
                                <span class="placeholder placeholder-xs col-7"           style="vertical-align:super;"></span>
                                <span class="placeholder placeholder-xs col-1 float-end" style="vertical-align:super;"></span>
                                <span class="placeholder placeholder-xs col-7"           style="vertical-align:super;"></span>
                                <span class="placeholder placeholder-xs col-1 float-end" style="vertical-align:super;"></span>
                                <span class="placeholder placeholder-xs col-7"           style="vertical-align:super;"></span>
                                <span class="placeholder placeholder-xs col-1 float-end" style="vertical-align:super;"></span>
                            </p>
                        </li>
                    </ul>

                </div>
                <!-- End::main-header-dropdown -->
            </li>
            <!-- End::header-element -->

{# user options #}
            <!-- Start::header-element -->
            <li class="header-element">
                <!-- Start::header-link|dropdown-toggle -->
                <a href="javascript:void(0);" class="header-link dropdown-toggle" id="mainHeaderProfile" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false">
                    <div class="d-flex align-items-center">
                        <div class="me-sm-2 me-0">

                            {# borrowed from avatars.html #}
                            {% if initials_for_avatar %}
                                <span
                                    class="avatar avatar-sm avatar-rounded bg-danger"
                                    data-bs-toggle="tooltip"
                                    data-bs-placement="left"
                                    data-bs-trigger="hover"
                                    data-bs-custom-class="tooltip-dark"
                                    title="{{tooltip_messages_for_avatar}}"
                                >{{initials_for_avatar}}</span>
                            {% else %}
                                <span class="avatar avatar-sm avatar-rounded">
                                    {% if request.user.is_superuser %}
                                        <img src="{% static 'files/profile-image/superuser.png' %}" alt="img">
                                    {% else %}
                                        <img src="{% static 'files/profile-image/non-superuser.png' %}" alt="img">
                                    {% endif %}
                                </span>
                            {% endif %}

                        </div>
                        <div class="d-xl-block d-none lh-1">
                            <span class="fw-medium lh-1">{{request.user.username}}</span>
                        </div>
                    </div>
                </a>
                <!-- End::header-link|dropdown-toggle -->
                <ul class="main-header-dropdown dropdown-menu pt-0 overflow-hidden header-profile-dropdown dropdown-menu-end" aria-labelledby="mainHeaderProfile">
                    <li><a class="dropdown-item d-flex align-items-center" href="https://tk.rahavardit.ir/" target="_blank"><i class="bi bi-headset fs-18 me-2 op-7"></i>Support</a></li>
                    {% if cafebazaar_url %}
                        <li><a class="dropdown-item d-flex align-items-center" href="{{cafebazaar_url}}" target="_blank"><i class="bi bi-phone fs-16 me-2 op-7"></i>Download App</a></li>
                    {% endif %}
                    <hr class="d-sm-block d-none my-0">
                    <li><a class="dropdown-item d-flex align-items-center" href="{% url "accounts-change-password-url" %}"><i class="bi bi-shield fs-18 me-2 op-7"></i>Change Password</a></li>
                    {% if request.user.is_superuser %}
                        <li><a class="dropdown-item d-flex align-items-center" href="{% url "admin:index" %}"><i class="bi bi-gear fs-16 me-2 op-7"></i>Admin Page</a></li>
                    {% endif %}
                    <li>
                        <a
                            data-bs-toggle="modal" data-bs-target="#LogoutModal"
                            class="dropdown-item d-flex align-items-center"
                            href="javascript:void(0);"
                        >
                            <i class="bi bi-box-arrow-right fs-18 me-2 op-7"></i>
                            Log Out
                        </a>
                    </li>
                </ul>
            </li>  
            <!-- End::header-element -->

{# switch #}
            <!-- Start::header-element -->
            <li class="header-element">
                <!-- Start::header-link|switcher-icon -->
                <a href="javascript:void(0);" class="header-link switcher-icon" data-bs-toggle="offcanvas" data-bs-target="#switcher-canvas">
                    <i class="bi bi-gear header-link-icon border-0"></i>
                </a>
                <!-- End::header-link|switcher-icon -->
            </li>
            <!-- End::header-element -->

        </ul>
        <!-- End::header-content-right -->

    </div>
    <!-- End::main-header-container -->

</header>
<!-- /app-header -->

<div class="modal fade" id="LogoutModal" tabindex="-1"
    aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="exampleModalLabel1">Log out</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                    aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Please confirm to log out
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light border border-muted" data-bs-dismiss="modal">Cancel</button>
                <a href="{% url "accounts-logout-url" %}" type="button" class="btn btn-primary-transparent">Confirm</a>
            </div>
        </div>
    </div>
</div>
